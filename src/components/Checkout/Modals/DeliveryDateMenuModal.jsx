import { useContext, useEffect, useState } from 'react';
import { Calendar } from '@hassanmojab/react-modern-calendar-datepicker';
import '@hassanmojab/react-modern-calendar-datepicker/lib/DatePicker.css';

import yordar from 'api/yordar';
import { CHECKOUT_DATE_VALIDATION } from 'api/endpoints';
import { TimeSlotList } from 'components/Checkout';
import { ClosureSupplier, Link } from 'components/Common';
import { dateAndTimeFromLocalStorage, getDateConfig } from 'utils/dateTime';
import useDeliveryDateStore from 'store/useDeliveryDateStore';
import { CategoryContext } from 'context/category';
import { UserContext } from 'context/user';
import { HostContext } from 'context/host';
import { useRouter } from 'next/router';
import { getUniqueSupplierIds } from 'utils/order';
import useDocketStore from 'store/useDocketStore';

const initialErrorState = {
  leadTime: null,
  isOutsideOperatingHours: false,
  isClosed: false,
  closureSuppliers: [],
};

const DeliveryDateMenuModal = () => {
  const { date, time } = dateAndTimeFromLocalStorage();

  const [selectedDay, setSelectedDay] = useState(date);
  const [selectedTime, setSelectedTime] = useState(time);
  const router = useRouter();

  const category = useContext(CategoryContext);
  const { user } = useContext(UserContext);
  const { appURL, marketingURL } = useContext(HostContext);

  const [deliveryError, setDeliveryError] = useState(initialErrorState);
  const hasErrors = deliveryError.leadTime || deliveryError.isOutsideOperatingHours || deliveryError.isClosed;
  const { setOpenDeliveryModal, setDateValidated, setDeliveryAt, dateErrors, setTime, setDate } = useDeliveryDateStore(
    (state) => ({
      setOpenDeliveryModal: state.setOpenDeliveryModal,
      setDateValidated: state.setDateValidated,
      setDeliveryAt: state.setDeliveryAt,
      setTime: state.setTime,
      setDate: state.setDate,
      dateErrors: state.dateErrors,
    })
  );

  const { orders } = useDocketStore((state) => ({
    orders: state.orders,
  }));

  function backToSuppliers() {
    setOpenDeliveryModal(false);
    router.push(`/search/${category || 'office-catering'}/${geoLocation.state}/${geoLocation.suburb}`);
  }

  let geoLocation;
  if (typeof window !== 'undefined') {
    geoLocation = JSON.parse(localStorage.getItem('geo-location'));
  }

  useEffect(() => setDeliveryError(dateErrors), [dateErrors]);

  function minimumDate() {
    const currentDate = new Date();
    const currentYear = currentDate.getFullYear();
    // Note: getMonth() returns 0-11, but react-modern-calendar-datepicker expects 1-12 for months
    const currentMonth = currentDate.getMonth() + 1;
    const currentDay = currentDate.getDate();

    return {
      year: currentYear,
      month: currentMonth,
      day: currentDay,
    };
  }

  function clearErrors() {
    setDeliveryError(initialErrorState);
  }

  const submitDeliveryDate = async () => {
    if (!selectedDay || !selectedTime) return;

    const deliveryDateObject = new Date(
      selectedDay.year,
      selectedDay.month - 1,
      selectedDay.day,
      selectedTime.hours,
      selectedTime.minutes
    );

    const dateConfig = getDateConfig(deliveryDateObject);

    const { data: validationData } = await yordar.get(CHECKOUT_DATE_VALIDATION, {
      params: {
        order_delivery_at: deliveryDateObject.toISOString(),
        supplier_ids: getUniqueSupplierIds(orders),
      },
      withCredentials: true,
    });

    if (!validationData.can_process_lead_time && !deliveryError.leadTime) {
      setDeliveryError((state) => ({
        ...state,
        leadTime: validationData.formatted_lead_time,
      }));
    } else if (
      (validationData.is_closed || validationData.outside_operating_hours) &&
      !deliveryError.closureSuppliers.length
    ) {
      setDeliveryError((state) => ({
        ...state,
        isOutsideOperatingHours: validationData.outside_operating_hours,
        isClosed: validationData.is_closed,
        closureSuppliers: validationData.supplier_closure_dates,
      }));
    } else {
      localStorage.setItem('deliveryDate', dateConfig.dateAndTime.date);
      localStorage.setItem('deliveryTime', selectedTime.display);
      setOpenDeliveryModal(false);
      setDeliveryAt(dateConfig.orderFormatted);
      setTime(dateConfig.dateAndTime.time);
      setDate(dateConfig.dateAndTime.date);
      setDateValidated(true);

      // Dispatch a custom event to signal that the modal is done
      const event = new CustomEvent('modalResponse', {
        detail: { success: true },
      });
      window.dispatchEvent(event);
    }
  };

  return (
    <div>
      <h3>Select Delivery Date & Time</h3>
      <p className="notice">
        Please note: Hot food may arrive up to 15 minutes prior to the delivery time, cold food may arrive up to 60
        minutes prior to the delivery time.
      </p>
      <div className="date-time-container">
        <Calendar
          value={selectedDay}
          onChange={(e) => {
            clearErrors();
            setSelectedDay(e);
          }}
          colorPrimary="#000"
          minimumDate={minimumDate()}
        />
        <TimeSlotList onChange={setSelectedTime} value={selectedTime.display} clearErrors={clearErrors} />
      </div>
      {!!deliveryError.leadTime && !deliveryError.closureSuppliers.length && (
        <p className="delivery-date-error">
          Your order requires a minimum lead time of {deliveryError.leadTime}. If you proceed with the order it may be
          rejected by the supplier(s)
        </p>
      )}
      {!!deliveryError.closureSuppliers.length && (
        <>
          {deliveryError.closureSuppliers.map((supplier) => (
            <div key={`supplier-closure-${supplier.id}`}>
              <ClosureSupplier supplier={supplier} />
            </div>
          ))}
          {deliveryError.isClosed && (
            <p className="delivery-date-error">Please place your order before or after these dates.</p>
          )}
          {!deliveryError.isClosed && deliveryError.isOutsideOperatingHours && (
            <p className="delivery-date-error">
              If you proceed the {deliveryError.closureSuppliers.length > 1 ? 'suppliers' : 'supplier'} may reject the
              order.
            </p>
          )}
        </>
      )}
      <button
        onClick={deliveryError.isClosed ? null : submitDeliveryDate}
        className={`button black ${deliveryError.isClosed ? 'disable-date' : ''}`}
        style={{ width: '100%', marginTop: '8px' }}
      >
        {(deliveryError.leadTime || deliveryError.isOutsideOperatingHours) && !deliveryError.isClosed
          ? 'Proceed Anyway'
          : 'Submit'}
      </button>
      {geoLocation && hasErrors && (
        <a className="button date-back" onClick={backToSuppliers}>
          Back To Suppliers
        </a>
      )}
      {!geoLocation && hasErrors && (
        <Link className="button date-back" href={user ? `${appURL}/c_profile` : marketingURL}>
          Home
        </Link>
      )}
    </div>
  );
};

export default DeliveryDateMenuModal;
