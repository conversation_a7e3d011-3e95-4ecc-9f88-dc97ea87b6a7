import { useState, useEffect } from 'react';
import moment from 'moment';
import { shallow } from 'zustand/shallow';

import {
  CheckoutModal,
  DeliveryAddress,
  DeliveryAddressModal,
  DeliveryDate,
  DeliveryDateModal,
  DeliveryInstruction,
  DeliveryInstructionModal,
  DeliveryWindows,
  DeliveryLoadingDock,
  LoadingDockHelper,
} from 'components/Checkout';
import useCheckoutStore from 'store/useCheckoutStore';
import { getDateConfig, parseDate } from 'utils/dateTime';
import { DYNAMIC_CHECKOUT_DATE_VALIDATION } from 'api/endpoints';
import yordar from 'api/yordar';

const DeliveryDetails = ({ isEditPage }) => {
  const { isWoolworthsOrder, orderID, status, setDate, requiresLoadingDockCode } = useCheckoutStore(
    (state) => ({
      isWoolworthsOrder: state.order.isWoolworthsOrder,
      orderID: state.order.id,
      status: state.order.status,
      setDate: state.setDate,
      requiresLoadingDockCode: state.checkout.requires_loading_dock_code,
    }),
    shallow
  );

  const initialErrorState = {
    leadTime: null,
    isOutsideOperatingHours: false,
    isClosed: false,
    closureSuppliers: [],
  };

  const [modalOpen, setModalOpen] = useState(false);
  const [modalInfo, setModalInfo] = useState(null);

  const [passedDateErrors, setPassedDateErrors] = useState(initialErrorState);

  let localStorageDate;
  let localStorageTime;

  if (typeof window !== 'undefined') {
    localStorageDate = localStorage.getItem('deliveryDate');
    localStorageTime = localStorage.getItem('deliveryTime');
  }

  const validateDeliveryAt = async () => {
    const dateTimeString = `${localStorageTime} ${localStorageDate}`;
    const dateTime = moment(dateTimeString, 'h:mm a D/M/YYYY');
    const isoDateString = dateTime.toISOString();
    const dateConfig = getDateConfig(dateTime.toDate());

    const { data } = await yordar.get(DYNAMIC_CHECKOUT_DATE_VALIDATION(orderID), {
      params: {
        order_delivery_at: isoDateString,
      },
      withCredentials: true,
    });

    if (!data.can_process_lead_time) {
      setModalOpen(true);
      setModalInfo('date');
      setPassedDateErrors((state) => ({
        ...state,
        leadTime: data.formatted_lead_time,
      }));
    } else if (data.is_closed || data.outside_operating_hours) {
      setModalOpen(true);
      setModalInfo('date');
      setPassedDateErrors((state) => ({
        ...state,
        isOutsideOperatingHours: data.outside_operating_hours,
        isClosed: data.is_closed,
        closureSuppliers: data.supplier_closure_dates,
      }));
    } else {
      setDate({
        deliveryDate: parseDate(localStorageDate),
        deliveryTime: localStorageTime,
        deliveryAt: dateConfig.orderFormatted,
        deliveryAtDisplayTime: dateConfig.displayFormatted.time,
        deliveryAtDisplayDate: dateConfig.displayFormatted.date,
      });
    }
  };

  function handleButtonClick(type) {
    setModalOpen(true);
    setModalInfo(type);
  }

  function getModalDimensions(type) {
    const dimensionOptions = { width: '460px' };
    if (type === 'date') {
      dimensionOptions.minHeight = '500px';
    } else if (type === 'deliveryWindow') {
      dimensionOptions.width = '600px';
      dimensionOptions.minHeight = '500px';
    } else if (type === 'loadingDockHelper') {
      dimensionOptions.minHeight = '300px';
    } else {
      dimensionOptions.height = '500px';
    }
    return dimensionOptions;
  }

  const renderModalContent = () => {
    switch (modalInfo) {
      case 'date':
        return <DeliveryDateModal setModalOpen={setModalOpen} passedDateErrors={passedDateErrors} isEditPage />;
      case 'address':
        return <DeliveryAddressModal setModalOpen={setModalOpen} />;
      case 'instructions':
        return <DeliveryInstructionModal setModalOpen={setModalOpen} />;
      case 'deliveryWindow':
        return <DeliveryWindows setModalOpen={setModalOpen} />;
      case 'loadingDockHelper':
        return <LoadingDockHelper setModalOpen={setModalOpen} />;
      default:
        return null;
    }
  };

  const requiresWoolworthsFields = isWoolworthsOrder && status === 'draft';

  useEffect(async () => {
    if (isEditPage) return;
    if (isWoolworthsOrder) {
      setModalOpen(true);
      setModalInfo('deliveryWindow');
    } else if (localStorageDate && localStorageTime) {
      await validateDeliveryAt();
    }
  }, [isWoolworthsOrder]);

  return (
    <>
      <div className="between-flex heading-block" style={{ borderTop: 'none' }}>
        <h3 className="section-heading" style={{ marginBottom: 0 }}>
          Delivery Details
        </h3>
      </div>
      <div className="checkout-section">
        <DeliveryAddress isWoolworthsOrder={requiresWoolworthsFields} handleButtonClick={handleButtonClick} />
        <DeliveryDate
          isWoolworthsOrder={requiresWoolworthsFields}
          handleButtonClick={handleButtonClick}
          setModalOpen={setModalOpen}
        />
        <DeliveryInstruction isWoolworthsOrder={requiresWoolworthsFields} handleButtonClick={handleButtonClick} />
        {requiresLoadingDockCode && <DeliveryLoadingDock handleButtonClick={handleButtonClick} />}
        <CheckoutModal open={modalOpen} setModalOpen={setModalOpen} dimensionOptions={getModalDimensions(modalInfo)}>
          {renderModalContent()}
        </CheckoutModal>
      </div>
    </>
  );
};

export default DeliveryDetails;
