import { useEffect, useContext, useState } from 'react';
import { useRouter } from 'next/router';
import { UserContext } from 'context/user';
import { toast } from 'react-toastify';
import Modal from 'react-responsive-modal';

import yordar from 'api/yordar';
import { defaultToastOptions, toastTypeOptions } from 'utils/toasterConfig';
import { DYNAMIC_ORDER_MINIMUM_CHECK_ENDPOINT } from 'api/endpoints';
import useCheckoutStore from 'store/useCheckoutStore';
import { CancelChangesModal, OrderMinimumError, ApproveOrderModal, QuoteButtons } from 'components/Checkout';

const CheckoutValidateButton = ({ moveToOrderSummary, canQuote = true, isEditPage }) => {
  const router = useRouter();
  const [modalOpen, setModalOpen] = useState(false);
  const [modalContent, setModalContent] = useState(null);
  const {
    order,
    validations,
    setOrderDetail,
    setValidationField,
    inProgress,
    setInProgress,
    submitOrder,
    validateWoolworthsOrder,
  } = useCheckoutStore((state) => ({
    order: state.order,
    validations: state.validations,
    setOrderDetail: state.setOrderDetail,
    setValidationField: state.setValidationField,
    inProgress: state.inProgress,
    setInProgress: state.setInProgress,
    submitOrder: state.submitOrder,
    validateWoolworthsOrder: state.validateWoolworthsOrder,
  }));
  const {
    user: { profile: customerProfile },
  } = useContext(UserContext);
  const isWoolworthsOrder = customerProfile && order.isWoolworthsOrder;

  const invalidFields = Object.keys(validations)?.filter((validation) => !validations[validation]);
  const { finaliseQuote } = router.query;

  const hasInvalidFields = invalidFields.length;

  const minimumErrorUserChoice = (data) =>
    new Promise((resolve) => {
      setModalContent(
        <OrderMinimumError
          order={order}
          response={data}
          setModalOpen={setModalOpen}
          awaitUserChoice={(choice) => {
            resolve(choice);
          }}
        />
      );
      setModalOpen(true);
    });

  const confirmOrderApproval = () =>
    new Promise((resolve) => {
      setModalContent(
        <ApproveOrderModal
          setModalOpen={setModalOpen}
          awaitUserChoice={(choice) => {
            resolve(choice);
          }}
        />
      );
      setModalOpen(true);
    });

  const handleSubmit = async ({ mode }) => {
    if (inProgress) return;

    setInProgress(mode);
    const hasNewCardOpen = !order.creditCardId && Object.keys(validations).includes('creditCardNum');
    try {
      if (isEditPage) {
        const { data } = await yordar.get(DYNAMIC_ORDER_MINIMUM_CHECK_ENDPOINT(order.id));
        if (data.is_under) {
          const minimumErrorChoice = await minimumErrorUserChoice(data);
          if (minimumErrorChoice === 'addMoreItems') {
            setInProgress(null);
            setTimeout(() => window.scrollTo(0, 0), 300);
            return;
          }
          // else continue
        }
      } else if (isWoolworthsOrder) {
        await validateWoolworthsOrder(true);
      }

      if (hasNewCardOpen) {
        // First validate Fields
        if (hasInvalidFields) {
          invalidFields.forEach((invalidField) => setValidationField(invalidField, false));
        }
        // Trigger Stripe Form submit
        setOrderDetail('submitNewCardWithMode', mode);
        return;
      }

      const redirectURL = await submitOrder({
        mode,
      });
      if (!isEditPage) {
        localStorage.removeItem('deliveryDate');
        localStorage.removeItem('deliveryTime');
      }
      if (isEditPage && redirectURL) {
        window.location = redirectURL;
      } else {
        router.push('checkout/success');
      }
    } catch (error) {
      console.log(`Error placing order from Place Order Button: ${error}`);
      setInProgress(null);

      // show error
      toast.dismiss();

      if (error.response) {
        const {
          response: {
            data: { errors: responseErrors },
          },
        } = error;
        responseErrors.forEach((error) =>
          toast.error(`${error}`, { ...defaultToastOptions, ...toastTypeOptions.info })
        );
      } else if (error.length) {
        for (let i = 0; i < error.length; i++) {
          if (error[i].message === 'Has Order Item Errors') {
            toast.warning('Order Validation failed, please Validate order again!', {
              ...defaultToastOptions,
              ...toastTypeOptions.info,
            });
            moveToOrderSummary();
          } else {
            toast.error(error[i].message, { ...defaultToastOptions, ...toastTypeOptions.info });
          }
        }
      } else {
        toast.error(error.message, { ...defaultToastOptions, ...toastTypeOptions.info });
      }
    }
  };

  let buttonText;
  const buttonInProgress = ['non-quote', 'one-off'].includes(inProgress);
  if (isEditPage && canQuote) {
    buttonText = buttonInProgress ? 'Approving Order...' : 'Approve Order';
  } else if (isEditPage && order.isRecurrent) {
    buttonText = buttonInProgress ? 'Updating Order As One Off...' : 'Update Only This Order';
  } else if (isEditPage) {
    buttonText = buttonInProgress ? 'Updating Order...' : 'Update Order';
  } else {
    buttonText = buttonInProgress ? 'Placing Order...' : 'Place Order';
  }

  useEffect(async () => {
    if (!finaliseQuote || !canQuote || !isEditPage || !order.id) return;

    window.scrollTo(0, document.body.scrollHeight);
    const approvalConfirmed = await confirmOrderApproval();
    if (approvalConfirmed) {
      handleSubmit({ mode: 'non-quote' });
    }
  }, [order.id]);

  if (isWoolworthsOrder && !isEditPage) {
    return (
      <>
        <button className="button checkout-button confirm primary" onClick={() => handleSubmit({ mode: 'non-quote' })}>
          {buttonText}
        </button>
        <button
          className="button checkout-button confirm secondary"
          onClick={() => {
            setValidationField('creditCardName', null, true);
            setValidationField('creditCardNum', null, true);
            setValidationField('creditCardId', null, true);
            handleSubmit({ mode: 'save-for-later' });
          }}
        >
          {inProgress === 'save-for-later' ? 'Saving for later...' : 'Save for later'}
        </button>
      </>
    );
  }

  return (
    <>
      <button
        className="button checkout-button confirm primary"
        onClick={() => handleSubmit({ mode: isEditPage ? 'one-off' : 'non-quote' })}
      >
        {buttonText}
      </button>
      {order.isRecurrent && isEditPage && (
        <button
          className="button checkout-button confirm secondary"
          onClick={() => handleSubmit({ mode: 'subsequent' })}
        >
          {inProgress === 'subsequent' ? 'Saving For All Future Orders...' : 'Save For All Future Orders'}
        </button>
      )}

      {canQuote && <QuoteButtons handleSubmit={handleSubmit} />}

      {isEditPage && (
        <button
          type="button"
          className="button checkout-button confirm cancel"
          onClick={() => {
            setModalContent(<CancelChangesModal setModalOpen={setModalOpen} />);
            setModalOpen(true);
          }}
        >
          Cancel Changes
        </button>
      )}
      {isEditPage && (
        <Modal
          open={modalOpen}
          onClose={() => setModalOpen(false)}
          center
          styles={{ modal: { width: '400px' } }}
          showCloseIcon={false}
          closeOnOverlayClick={false}
          animationDuration={100}
        >
          {modalContent}
        </Modal>
      )}
    </>
  );
};

export default CheckoutValidateButton;
