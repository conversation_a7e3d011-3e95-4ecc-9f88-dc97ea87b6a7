import { useState } from 'react';
import axios from 'axios';

const TeamAttendeePackage = ({ packageOrder }) => {
  const attendeeStatus = packageOrder.team_order_attendee.status;
  const [attendeeUnsubscribed, setAttendeeUnsubscribed] = useState(false);
  const orderLines = packageOrder?.team_order_attendee?.order_lines;
  const attendeeOrdered = attendeeStatus === 'ordered';
  const hasDeclined = attendeeStatus === 'declined' || attendeeUnsubscribed;
  const expired = packageOrder.cutoff.has_expired;

  async function unsubscribeAttendee(e) {
    e.preventDefault();
    const { data } = await axios({ url: packageOrder.team_order_attendee.unsubscribe_url });
    if (data.success) {
      setAttendeeUnsubscribed(true);
    }
  }

  function getAttendeeCardOptions() {
    if (expired && !attendeeOrdered) {
      return { class: 'expired', declineButton: 'Order Passed' };
    }
    if (hasDeclined) {
      return { class: 'declined', declineButton: 'Not Attending' };
    }
    if (!expired && !attendeeOrdered) {
      return { class: 'invited', declineButton: "I'm not attending" };
    }
    if (attendeeOrdered && !expired) {
      return { class: 'ordered', declineButton: "I'm not attending" };
    }
    if (attendeeOrdered && expired) {
      return { class: 'past-order', declineButton: 'Order Passed' };
    }
  }

  const options = getAttendeeCardOptions();

  return (
    <div className="team-attendee-day">
      <p className={`expiry-text ${options.class}`}>{packageOrder.delivery_at}</p>
      <div className="team-attendee-card">
        <div className="team-attendee-supplier-image">
          <img src={packageOrder.suppliers[0].image} />
        </div>
        <div className="team-attendee-supplier-card__details">
          <p style={{ fontWeight: 'bold', marginBottom: '4px' }}>{packageOrder.suppliers[0].name}</p>
          {!hasDeclined && (
            <p className="team-orderline-info">{orderLines.map((ol) => `${ol.quantity}x ${ol.name}`).join(', ')}</p>
          )}
          <div className="expiry-container">
            {!attendeeOrdered && !hasDeclined && <p>{packageOrder.expiry_options.text}</p>}
            {hasDeclined && <p className="declined">You Declined Your Order</p>}
          </div>
        </div>
        <div className="team-attendee-button-container">
          <a
            className={`button ${options.class}`}
            onClick={(e) => (hasDeclined && !expired ? null : unsubscribeAttendee(e))}
          >
            {options.declineButton}
          </a>
          {!expired && (
            <a className={`button ${expired ? 'gray-btn' : ''}`} href={packageOrder.team_order_attendee.order_url}>
              {attendeeOrdered ? 'Edit Order' : 'Order'}
            </a>
          )}
        </div>
      </div>
    </div>
  );
};

export default TeamAttendeePackage;
