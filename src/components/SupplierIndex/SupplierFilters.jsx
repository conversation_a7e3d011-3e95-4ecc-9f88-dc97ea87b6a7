import { useEffect, useState } from 'react';
import Link from 'next/link';
import { useRouter } from 'next/router';

import useDeliveryDateStore from 'store/useDeliveryDateStore';
import { SearchBar } from 'components/Common';
import { CheckoutModal, DeliveryDateSupplierModal } from 'components/Checkout';
import useFilterStore from 'store/useFilterStore';

const SupplierFilters = ({ categories, showNav, toggleNav }) => {
  const {
    delivery_date: deliveryDate,
    delivery_time: deliveryTime,
    setDate,
    setTime,
    clearDate,
  } = useDeliveryDateStore((state) => state);
  const router = useRouter();
  const { category, state, suburb, date: queryDate } = router.query;
  const { filters, clearFilters } = useFilterStore((store) => ({
    filters: store.filters,
    clearFilters: store.clearFilters,
  }));
  let localStorageDate;
  let localStorageTime;
  if (typeof window !== 'undefined') {
    if (queryDate) {
      localStorage.setItem('deliveryDate', queryDate);
      localStorage.setItem('deliveryTime', '10:00 am');
    }
    localStorageDate = localStorage?.getItem('deliveryDate');
    localStorageTime = localStorage?.getItem('deliveryTime');
  }

  useEffect(() => {
    if (localStorageDate && localStorageTime) {
      setTime(localStorageTime);
      setDate(localStorageDate);
    }
  }, []);

  const [modalOpen, setModalOpen] = useState(false);

  const filterSet = category === 'office-catering' ? 'catering-services' : 'kitchen-supplies';
  const categoryTitle = category === 'office-catering' ? 'Cuisines' : 'Categories';
  const hasDateSet = deliveryTime && deliveryDate;

  return (
    <div className={`sticky-wrapper ${showNav ? 'mobile-open' : 'mobile-closed'}`}>
      <div className="filters">
        <div className="filters-header">
          <div className="filters-heading">
            <button
              type="button"
              onClick={toggleNav}
              className="icon icon-close filters-close-btn"
              aria-label="Close Filter"
            />
            <span>Filters</span>
          </div>
          <div className="filters-section">
            <SearchBar navigation />
          </div>
          <div
            className="filters-section"
            style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}
          >
            <div
              className={`icon icon-large icon-right-spacer icon-calendar order-date-change ${hasDateSet ? 'set' : ''}`}
              onClick={() => setModalOpen(true)}
            >
              {hasDateSet ? `${deliveryTime}, ${deliveryDate}` : 'Set Delivery Date'}
            </div>
            {hasDateSet && (
              <div className="icon icon-large icon-cancel-black" onClick={clearDate} style={{ cursor: 'pointer' }} />
            )}
          </div>
          <div className="filters-section">
            <h5
              className={`category-navigation ${
                category === 'office-catering' ? 'selected' : ''
              } icon icon-large icon-right-spacer icon-catering`}
            >
              <Link href={`/search/office-catering/${state}/${suburb}`}>Office Catering</Link>
            </h5>
            <h5
              className={`category-navigation ${
                category === 'office-snacks' ? 'selected' : ''
              } icon icon-large icon-right-spacer icon-snacks`}
            >
              <Link href={`/search/office-snacks/${state}/${suburb}`}>Office Snacks</Link>
            </h5>
          </div>
        </div>
        <div className="filters-body">
          <div className="filters-section">
            <h5 className="icon icon-large icon-right-spacer icon-sort">Sort</h5>
            {categories.other.sort().map((cat) => (
              <Filter key={cat} category={cat} checked={filters.includes(cat)} />
            ))}
          </div>
          <div className="filters-section">
            <h5 className="icon icon-large icon-right-spacer icon-cuisine">{categoryTitle}</h5>
            {categories[filterSet].sort().map((cat) => (
              <Filter key={cat} category={cat} checked={filters.includes(cat)} />
            ))}
          </div>
          {category === 'office-catering' && (
            <div className="filters-section">
              <>
                <h5 className="icon icon-large icon-right-spacer icon-dietary">Dietaries</h5>
                {categories.dietary.sort().map((cat) => (
                  <Filter key={cat} category={cat} checked={filters.includes(cat)} />
                ))}
              </>
            </div>
          )}
        </div>
        <CheckoutModal
          open={modalOpen}
          setModalOpen={setModalOpen}
          dimensionOptions={{ width: '460px', minHeight: '500px' }}
          showCloseIcon
        >
          <DeliveryDateSupplierModal setModalOpen={setModalOpen} />
        </CheckoutModal>
        <div className="filters__buttons">
          <button type="button" onClick={() => clearFilters()} className="button black outline">
            Clear All
          </button>
          <button type="button" onClick={toggleNav} className="button black">
            Done
          </button>
        </div>
      </div>
    </div>
  );
};

const Filter = ({ category, checked }) => {
  const toggleFilter = useFilterStore((state) => state.toggleFilter);

  return (
    <>
      <label htmlFor={`filter-${category}`} className={`filter ${checked ? 'checked' : ''}`}>
        <input
          id={`filter-${category}`}
          type="checkbox"
          onChange={(e) => handleFilter({ e, toggleFilter })}
          data-name={category}
          checked={checked}
        />
        {category}
      </label>
    </>
  );
};

const handleFilter = ({ e, toggleFilter }) => {
  const { name } = e.target.dataset;
  toggleFilter(name);
  window.scrollTo(0, 0);
};

export default SupplierFilters;
