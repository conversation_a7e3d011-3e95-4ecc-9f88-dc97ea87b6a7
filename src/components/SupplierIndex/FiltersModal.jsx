import { useEffect } from 'react';
import { useRouter } from 'next/router';

import useDeliveryDateStore from 'store/useDeliveryDateStore';
import useFilterStore from 'store/useFilterStore';

const FiltersModal = ({ categories }) => {
  const { setDate, setTime } = useDeliveryDateStore((state) => state);
  const router = useRouter();
  const { category, date: queryDate } = router.query;
  const { filters } = useFilterStore((store) => ({
    filters: store.filters,
    clearFilters: store.clearFilters,
  }));
  let localStorageDate;
  let localStorageTime;
  if (typeof window !== 'undefined') {
    if (queryDate) {
      localStorage.setItem('deliveryDate', queryDate);
      localStorage.setItem('deliveryTime', '10:00 am');
    }
    localStorageDate = localStorage?.getItem('deliveryDate');
    localStorageTime = localStorage?.getItem('deliveryTime');
  }

  useEffect(() => {
    if (localStorageDate && localStorageTime) {
      setTime(localStorageTime);
      setDate(localStorageDate);
    }
  }, []);

  const filterSet = category === 'office-catering' ? 'catering-services' : 'kitchen-supplies';
  const categoryTitle = category === 'office-catering' ? 'Cuisines' : 'Categories';

  return (
    <div className="filters">
      <div>
        <div className="filters-section" style={{ padding: 0 }}>
          <h5 className="icon icon-large icon-right-spacer icon-sort">Sort</h5>
          {categories.other.sort().map((cat) => (
            <Filter key={cat} category={cat} checked={filters.includes(cat)} />
          ))}
        </div>
        <div className="filters-section" style={{ padding: 0 }}>
          <h5 className="icon icon-large icon-right-spacer icon-cuisine">{categoryTitle}</h5>
          {categories[filterSet].sort().map((cat) => (
            <Filter key={cat} category={cat} checked={filters.includes(cat)} />
          ))}
        </div>
        {category === 'office-catering' && (
          <div className="filters-section" style={{ padding: 0 }}>
            <>
              <h5 className="icon icon-large icon-right-spacer icon-dietary">Dietaries</h5>
              {categories.dietary.sort().map((cat) => (
                <Filter key={cat} category={cat} checked={filters.includes(cat)} />
              ))}
            </>
          </div>
        )}
      </div>
    </div>
  );
};

const Filter = ({ category, checked }) => {
  const toggleFilter = useFilterStore((state) => state.toggleFilter);

  return (
    <>
      <label htmlFor={`filter-${category}`} className={`filter ${checked ? 'checked' : ''}`}>
        <input
          id={`filter-${category}`}
          type="checkbox"
          onChange={(e) => handleFilter({ e, toggleFilter })}
          data-name={category}
          checked={checked}
        />
        {category}
      </label>
    </>
  );
};

const handleFilter = ({ e, toggleFilter }) => {
  const { name } = e.target.dataset;
  toggleFilter(name);
  window.scrollTo(0, 0);
};

export default FiltersModal;
