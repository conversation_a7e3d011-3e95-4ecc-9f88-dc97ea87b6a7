import useFilterStore from 'store/useFilterStore';

const FilterTags = ({ filters }) => {
  const toggleFilter = useFilterStore((state) => state.toggleFilter);
  const dateFilterRegex = /^\d{1,2}\/\d{1,2}\/\d{4}$/;
  const isDateFilter = (filter) => dateFilterRegex.test(filter);

  function clearDateCookie() {
    const domain = process.env.NEXT_PUBLIC_ENVIRONMENT_DOMAIN;
    const name = 'delivery_date_filter';
    const date = new Date();
    const oneDayInMilliseconds = 1000 * 60 * 60 * 24;
    date.setTime(date.getTime() - oneDayInMilliseconds);
    const expires = `; expires=${date.toUTCString()}`;
    const domainAttribute = domain ? `; domain=${domain}` : '';
    document.cookie = `${name}=;${expires}${domainAttribute}; path=/`;
  }

  return (
    <div className="filter-tag-row">
      <ul>
        <span>Filtering Results By:</span>
        {filters.map((filterTag) => (
          <li
            key={filterTag}
            className="filter-tag"
            onClick={() => {
              if (isDateFilter(filterTag)) {
                clearDateCookie();
              }
              toggleFilter(filterTag);
            }}
            role="presentation"
          >
            {filterTag}
          </li>
        ))}
      </ul>
    </div>
  );
};

export default FilterTags;
