import React from 'react';
import Image from 'next/image';
import Link from 'next/link';
import LogRocket from 'logrocket';

class ErrorBoundary extends React.Component {
  constructor(props) {
    super(props);
    this.state = { hasError: false, sessionURL: '', loading: true };
  }

  static getDerivedStateFromError() {
    return { hasError: true };
  }

  componentDidCatch(error, errorInfo) {
    console.log({ error, errorInfo });

    LogRocket.getSessionURL((sessionURL) => {
      this.setState({ sessionURL, loading: false });
    });
  }

  render() {
    const { hasError, loading, sessionURL } = this.state;
    const { children } = this.props;

    if (hasError) {
      return (
        <div>
          <div className="error-container" style={{ textAlign: 'center', paddingTop: '80px' }}>
            <Image src="/error.png" alt="error-illustration" width={340} height={340} />
            <h2 style={{ marginBottom: '4px' }}>Hmm..Something went wrong</h2>
            <p style={{ marginBottom: '8px' }}>
              If this issue persists, please share this session ID with our support team:
            </p>
            {loading && <Spinner />}
            {!loading && (
              <p style={{ marginBottom: 0, fontStyle: 'italic' }}>
                {sessionURL.replace('https://app.logrocket.com', '')}
              </p>
            )}
            <Link href="/">
              <a className="button">Go Back home</a>
            </Link>
          </div>
        </div>
      );
    }

    return children;
  }
}

const Spinner = () => (
  <div className="spinner small black">
    <div />
    <div />
    <div />
    <div />
  </div>
);

export default ErrorBoundary;
