import { useContext, useEffect, useState } from 'react';
import { useRouter } from 'next/router';

import yordar from 'api/yordar';
import { DYNAMIC_DELIVERY_DETAILS_ENDPOINT } from 'api/endpoints';
import { Image, Link } from 'components/Common';
import { UserContext } from 'context/user';
import { CategoryContext } from 'context/category';
import { HostContext } from 'context/host';

const SupplierBanner = ({ listing }) => {
  const [deliveryDetails, setDeliveryDetails] = useState({
    operatingDays: listing.operating_days,
    operatingHours: listing.operating_hours,
    deliveryFee: listing.delivery_fee,
  });

  const router = useRouter();
  const { mealUUID } = router.query;

  const { appURL, marketingURL } = useContext(HostContext);

  const { user } = useContext(UserContext);
  const category = useContext(CategoryContext);

  useEffect(() => {
    const missingDetails =
      !deliveryDetails.operatingHours || !deliveryDetails.operatingDays || !deliveryDetails.deliveryFee;
    if (listing && missingDetails) fetchSupplierDeliveryDetails(listing.id, setDeliveryDetails);
  }, [listing]);

  let geoLocation;
  if (typeof window !== 'undefined') {
    geoLocation = JSON.parse(localStorage.getItem('geo-location'));
  }

  return (
    <>
      <div className="supplier-banner">
        <Image
          url={listing.image_id}
          width={800}
          height={440}
          alt={`${listing.name}-image`}
          className="supplier-banner-image"
        />
        <div className="supplier-banner-details">
          {geoLocation && (
            <div className="supplier-banner-navigation">
              <Link className="supplier-banner-navigation__link" href={user ? `${appURL}/c_profile` : marketingURL}>
                Home
              </Link>
              <span> / </span>
              <Link
                className="supplier-banner-navigation__link"
                href={`/search/${category || 'office-catering'}/${geoLocation.state}/${geoLocation.suburb}${
                  mealUUID ? `?mealUUID=${mealUUID}` : ''
                }`}
              >
                Suppliers
              </Link>
              <span> / </span>
              <span>{listing.name}</span>
            </div>
          )}
          <h1>{listing.name}</h1>
          <SupplierDeliveryDetails
            deliveryDetails={deliveryDetails}
            minOrder={listing.min_order}
            leadTime={listing.lead_time}
          />
          <SupplierRating rating={listing.rating} ratingCount={listing.rating_count} />
          <SupplierIcons tooltip={listing.tooltip} />
        </div>
      </div>
      {!!listing.liquor_license_no && <LiquorLicence number={listing.liquor_license_no} />}
    </>
  );
};

const SupplierDeliveryDetails = ({ deliveryDetails, minOrder, leadTime }) => (
  <>
    <p className="supplier-banner-details-info">
      {deliveryDetails.operatingHours
        ? `${deliveryDetails.operatingDays} | ${deliveryDetails.operatingHours}`
        : ' Loading...'}
    </p>
    <p className="supplier-banner-details-info">
      {deliveryDetails.deliveryFee ? deliveryDetails.deliveryFee : 'Free Delivery'} |{' '}
      {minOrder ? `Minimum Order ${minOrder}` : 'No Min Order'}
    </p>
    <p className="supplier-banner-details-info">Lead Time: {leadTime || 'No Lead Time Available'}</p>
  </>
);

const SupplierRating = ({ rating, ratingCount }) => (
  <div>
    <p>
      {rating > 0 && <span>{rating.toFixed(1)} | </span>}
      <span>{ratingCount} Ratings</span>
    </p>
  </div>
);

const SupplierIcons = ({ tooltip }) => (
  <div className="supplier-icons">
    {tooltip.map((tip) => (
      <SupplierIcon key={tip.label} tip={tip} />
    ))}
  </div>
);

const SupplierIcon = ({ tip }) => {
  const [tooltipActive, setTooltipActive] = useState(false);
  return (
    // eslint-disable-next-line
    <span
      onMouseEnter={() => setTooltipActive(true)}
      onMouseLeave={() => setTooltipActive(false)}
    >
      <span key={tip.label} className={`supplier-icon icon icon-${tip.icon}`} />
      {tooltipActive && <p className="supplier-tooltip">{tip.label}</p>}
    </span>
  );
};

const LiquorLicence = ({ number }) => (
  <div className="liquor-licence-warning">
    WARNING - Under the Liquor Control Reform Act 1998 it is an offence: To supply alcohol to a person under the age of
    18 years [Penalty exceeds $19,000]; For a person under the age of 18 years to purchase or receive liquor [Penalty
    exceeds $800]. Liquor Licence: {number}
  </div>
);

async function fetchSupplierDeliveryDetails(supplierID, setDeliveryDetails) {
  const {
    data: { operating_hours: operatingHours, operating_days: operatingDays, delivery_fee: deliveryFee },
  } = await yordar.get(DYNAMIC_DELIVERY_DETAILS_ENDPOINT(supplierID), { withCredentials: true });

  if (!operatingHours || !operatingDays || !deliveryFee)
    return setDeliveryDetails({
      operatingDays: 'Please set Suburb',
      operatingHours: 'Please set Suburb',
      deliveryFee: 'Set Suburb',
    });
  setDeliveryDetails({
    operatingDays,
    operatingHours,
    deliveryFee,
  });
}

export default SupplierBanner;
