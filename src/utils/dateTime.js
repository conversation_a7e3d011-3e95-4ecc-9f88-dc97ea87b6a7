function format12Hour(date) {
  let hours = date.getHours();
  let minutes = date.getMinutes();
  const ampm = hours >= 12 ? 'pm' : 'am';
  hours %= 12;
  hours = hours || 12;
  minutes = minutes < 10 ? `0${minutes}` : minutes;
  return `${hours}:${minutes} ${ampm}`;
}

export function generateTimeSlots() {
  const slots = [];
  const now = new Date();
  let closestFutureSlot = '';
  let minimumDifference = Infinity;

  for (let hours = 0; hours < 24; hours += 1) {
    if (hours >= 23 || hours < 4) continue; // Skip times between 11 PM and 4 AM

    for (let minutes = 0; minutes < 60; minutes += 15) {
      const time = new Date(now);
      time.setHours(hours, minutes, 0, 0);
      if (time > now) {
        // Future slot
        const difference = time - now;
        if (difference < minimumDifference) {
          minimumDifference = difference;
          closestFutureSlot = { time: format12Hour(time), hours, minutes };
        }
      }
      slots.push({ display: format12Hour(time), hours, minutes });
    }
  }
  return { slots, closestFutureSlot };
}

export function calculateDate(daysFromToday) {
  if (typeof daysFromToday !== 'number') return new Date();
  const date = new Date();
  date.setDate(date.getDate() + daysFromToday);
  return date;
}

function formatDateForDisplay(date) {
  const timeFormatter = new Intl.DateTimeFormat('en-US', {
    hour: 'numeric',
    minute: 'numeric',
    hour12: true,
    weekday: 'long',
  });

  // Create an Intl.DateTimeFormat instance for the date part
  const dateFormatter = new Intl.DateTimeFormat('en-US', {
    month: 'short',
    day: 'numeric',
    year: 'numeric',
  });

  // Format the date and time separately
  const timeString = timeFormatter.format(date);
  const dateString = dateFormatter.format(date);

  return { time: timeString, date: dateString };
}

function formatDateForOrder(date) {
  const formattedDate = new Intl.DateTimeFormat('en-UK', { dateStyle: 'short' }).format(date);
  const formattedTime = new Intl.DateTimeFormat('en', { timeStyle: 'short' }).format(date).toLowerCase();
  return `${formattedTime}, ${formattedDate}`;
}

function getDateAndTime(date) {
  // Or any specific date
  const timeOptions = {
    hour: 'numeric',
    minute: '2-digit',
    hour12: true,
  };
  const day = date.getDate();
  const month = date.getMonth() + 1; // Months are zero-based, so we add 1
  const year = date.getFullYear();
  const formattedDate = `${day}/${month}/${year}`;
  return {
    time: date.toLocaleTimeString('en-GB', timeOptions),
    date: formattedDate,
  };
}

export const getDateConfig = (deliveryDate) => {
  if (!deliveryDate) return null;
  return {
    apiFormatted: deliveryDate.toISOString(),
    displayFormatted: formatDateForDisplay(deliveryDate),
    orderFormatted: formatDateForOrder(deliveryDate),
    dateAndTime: getDateAndTime(deliveryDate),
  };
};

export const dateAndTimeFromLocalStorage = () => {
  let initialDate = null;
  let initialTime = '';
  if (typeof window !== 'undefined') {
    const localStorageDate = localStorage.getItem('deliveryDate');
    const localStorageTime = localStorage.getItem('deliveryTime');
    if (localStorageDate) {
      const [day, month, year] = localStorageDate.split('/').map(Number);
      initialDate = { day, month, year };
    }
    if (localStorageTime) {
      const [time, period] = localStorageTime.split(' ');
      let [hours, minutes] = time.split(':').map(Number);
      if (period === 'pm' && hours < 12) {
        hours += 12;
      }
      if (period === 'am' && hours === 12) {
        hours = 0;
      }
      initialTime = { display: localStorageTime, hours, minutes };
    }
  }
  return { date: initialDate, time: initialTime };
};

export const parseDate = (dateStr) => {
  const [day, month, year] = dateStr.split('/').map(Number);
  return { year, month, day };
};
