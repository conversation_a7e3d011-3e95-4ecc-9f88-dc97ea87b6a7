import yordar from 'api/yordar';

import {
  CATEGORIES_ENDPOINT,
  DYNAMIC_ATTENDEE_CHECKOUT_ENDPOINT,
  DYNAMIC_SUPPLIERS_ENDPOINT,
  DYNAMIC_SESSION_ORDER_ENDPOINT,
} from 'api/endpoints';

import { formatCategoryForApi } from 'utils/format';
import { dietaryCategories, extraCategories } from 'static/categories';
import getBaseURL from './getBaseURL';

export const fetchSuppliers = async ({ query, cookies, host, mealUUID = '' }) => {
  const { state: locationState, suburb, category } = query;
  const formattedSuburb = suburb.humanize().replace(/ /g, '%20');
  const formattedState = locationState.toUpperCase();
  const formattedCategory = formatCategoryForApi(category);

  const { data } = await yordar.get(
    DYNAMIC_SUPPLIERS_ENDPOINT({
      suburb: formattedSuburb,
      state: formattedState,
      category_group: formattedCategory,
      mealUUID,
      wants_filter_data: true,
    }),
    {
      withCredentials: true,
      headers: cookies ? { Cookie: cookies } : {},
      baseURL: getBaseURL({ reqHost: host, type: 'app' }),
    }
  );
  return data;
};

export async function fetchSessionOrder({ cookies, query }) {
  try {
    const { data } = await yordar.get(DYNAMIC_SESSION_ORDER_ENDPOINT(query), {
      withCredentials: true,
      headers: cookies ? { Cookie: cookies } : {},
    });
    return data;
  } catch {
    console.error('Failed to fetch session order from rails');
    return null;
  }
}

export async function postAttendeeOrder(uuid, level) {
  let data;
  try {
    const response = await yordar(DYNAMIC_ATTENDEE_CHECKOUT_ENDPOINT(uuid), {
      method: 'post',
      withCredentials: true,
      data: level ? { team_order_level_id: level } : {},
    });
    data = response.data;
  } catch (err) {
    data = err.response.data;
    console.error('Failed to post attendee order', err);
  }
  return data;
}

export async function getCategories() {
  try {
    const { data: apiCategories } = await yordar.get(CATEGORIES_ENDPOINT);

    // Add 'Diwali' to catering-services
    apiCategories['catering-services'] = [...apiCategories['catering-services'], 'Diwali'];

    // Combine with static categories
    const allCategories = {
      ...apiCategories,
      dietary: dietaryCategories,
      other: extraCategories,
    };

    return allCategories;
  } catch (error) {
    console.error('Failed to fetch categories:', error);
    return {
      'catering-services': ['Diwali'],
      'kitchen-supplies': [],
      dietary: dietaryCategories,
      other: extraCategories,
    };
  }
}
